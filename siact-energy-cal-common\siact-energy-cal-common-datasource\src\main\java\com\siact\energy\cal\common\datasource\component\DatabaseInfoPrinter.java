package com.siact.energy.cal.common.datasource.component;

import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据库信息打印组件
 * 在应用启动时打印数据库相关信息
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Component
public class DatabaseInfoPrinter implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInfoPrinter.class);

    @Autowired
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    @Override
    public void run(String... args) throws Exception {
        try {
            printDatabaseInfo();
        } catch (Exception e) {
            logger.warn("Failed to print database information: {}", e.getMessage());
        }
    }

    private void printDatabaseInfo() {
        logger.info("=== Database Compatibility Information ===");

        DatabaseCompatibilityUtil.DatabaseType dbType = databaseCompatibilityUtil.detectDatabaseType();
        logger.info("Database Type: {}", dbType.getName());
        logger.info("Database Product: {}", databaseCompatibilityUtil.getDatabaseProductName());
        logger.info("Database Version: {}", databaseCompatibilityUtil.getDatabaseProductVersion());
        logger.info("Driver Name: {}", databaseCompatibilityUtil.getDriverName());
        logger.info("Driver Version: {}", databaseCompatibilityUtil.getDriverVersion());

        // 特别针对南大通用数据库的检测信息
        String productName = databaseCompatibilityUtil.getDatabaseProductName();
        if (productName != null && (productName.toLowerCase().contains("gbase") || productName.contains("南大通用"))) {
            logger.info("*** 检测到南大通用数据库 ***");
            logger.info("Product Name: {}", productName);
            logger.info("使用 PostgreSQL 兼容模式");
        }
        
        logger.info("=== Database Feature Support ===");
        logger.info("Supports ON DUPLICATE KEY UPDATE: {}", databaseCompatibilityUtil.supportsOnDuplicateKeyUpdate());
        logger.info("Supports ON CONFLICT: {}", databaseCompatibilityUtil.supportsOnConflict());
        logger.info("Supports MERGE: {}", databaseCompatibilityUtil.supportsMerge());
        
        logger.info("=== Database Compatibility Status ===");
        if (databaseCompatibilityUtil.isMysql()) {
            logger.info("MySQL database detected - Full compatibility");
        } else if (databaseCompatibilityUtil.isPostgreSQL()) {
            logger.info("PostgreSQL-based database detected - Using ON CONFLICT syntax");
            if (dbType == DatabaseCompatibilityUtil.DatabaseType.KINGBASE) {
                logger.info("KingbaseES (人大金仓) database detected - Using PostgreSQL compatibility mode");
            } else {
                logger.info("Standard PostgreSQL or compatible database detected");
                // 特别检查是否为南大通用
                if (productName != null && (productName.toLowerCase().contains("gbase") || productName.contains("南大通用"))) {
                    logger.info("南大通用数据库 - 使用 PostgreSQL 兼容模式");
                }
            }
        } else if (databaseCompatibilityUtil.isOracle()) {
            logger.info("Oracle database detected - Using MERGE syntax");
        } else if (databaseCompatibilityUtil.isSqlServer()) {
            logger.info("SQL Server database detected - Using MERGE syntax");
        } else if (databaseCompatibilityUtil.isDM()) {
            logger.info("DM (达梦) database detected - Using compatible syntax");
        } else {
            logger.warn("Unknown database type detected - Using MySQL fallback syntax");
        }

        logger.info("Supports ON DUPLICATE KEY UPDATE: {}", databaseCompatibilityUtil.supportsOnDuplicateKeyUpdate());
        logger.info("Supports ON CONFLICT: {}", databaseCompatibilityUtil.supportsOnConflict());
        logger.info("Supports MERGE: {}", databaseCompatibilityUtil.supportsMerge());

        logger.info("==========================================");
    }
}
