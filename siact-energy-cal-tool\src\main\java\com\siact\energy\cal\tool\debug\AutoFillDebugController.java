package com.siact.energy.cal.tool.debug;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.tool.entity.flow.ComponentFlowEntity;
import com.siact.energy.cal.tool.service.flow.IProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动填充调试控制器
 * 用于测试tool模块BaseEntity的自动填充功能
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/debug/autofill")
@Api(tags = "自动填充调试接口")
public class AutoFillDebugController {

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Autowired(required = false)
    private IProcessService processService;

    @GetMapping("/info")
    @ApiOperation("获取自动填充配置信息")
    public Map<String, Object> getAutoFillInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // MetaObjectHandler信息
            Map<String, Object> handlerInfo = new HashMap<>();
            if (metaObjectHandler != null) {
                handlerInfo.put("exists", true);
                handlerInfo.put("className", metaObjectHandler.getClass().getName());
                handlerInfo.put("packageName", metaObjectHandler.getClass().getPackage().getName());
            } else {
                handlerInfo.put("exists", false);
                handlerInfo.put("error", "MetaObjectHandler未注入");
            }
            result.put("metaObjectHandler", handlerInfo);

            // 服务信息
            Map<String, Object> serviceInfo = new HashMap<>();
            serviceInfo.put("processServiceExists", processService != null);
            result.put("services", serviceInfo);

            result.put("status", "success");
            result.put("message", "自动填充配置信息获取成功");

        } catch (Exception e) {
            log.error("获取自动填充配置信息失败", e);
            result.put("status", "error");
            result.put("message", "获取配置信息失败: " + e.getMessage());
        }

        return result;
    }

    @GetMapping("/test-insert")
    @ApiOperation("测试插入自动填充")
    public Map<String, Object> testInsertAutoFill() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (processService == null) {
                result.put("status", "error");
                result.put("message", "ProcessService未注入，无法测试");
                return result;
            }

            // 创建测试实体
            ComponentFlowEntity testEntity = new ComponentFlowEntity();
            testEntity.setFlowName("自动填充测试_" + System.currentTimeMillis());
            testEntity.setContext("{}");
            testEntity.setFlowDec("测试自动填充功能");
            testEntity.setStatus(0);
            
            log.info("插入前的实体状态:");
            log.info("createTime: {}", testEntity.getCreateTime());
            log.info("updateTime: {}", testEntity.getUpdateTime());
            log.info("createId: {}", testEntity.getCreateId());
            log.info("createName: {}", testEntity.getCreateName());

            // 执行插入
            boolean success = processService.save(testEntity);
            
            log.info("插入后的实体状态:");
            log.info("createTime: {}", testEntity.getCreateTime());
            log.info("updateTime: {}", testEntity.getUpdateTime());
            log.info("createId: {}", testEntity.getCreateId());
            log.info("createName: {}", testEntity.getCreateName());

            Map<String, Object> testResult = new HashMap<>();
            testResult.put("insertSuccess", success);
            testResult.put("entityId", testEntity.getId());
            
            Map<String, Object> autoFillResult = new HashMap<>();
            autoFillResult.put("createTime", testEntity.getCreateTime());
            autoFillResult.put("updateTime", testEntity.getUpdateTime());
            autoFillResult.put("createId", testEntity.getCreateId());
            autoFillResult.put("createName", testEntity.getCreateName());
            
            testResult.put("autoFillFields", autoFillResult);
            
            // 判断自动填充是否成功
            boolean autoFillWorking = testEntity.getCreateTime() != null && 
                                    testEntity.getUpdateTime() != null;
            testResult.put("autoFillWorking", autoFillWorking);
            
            result.put("testResult", testResult);
            result.put("status", "success");
            result.put("message", autoFillWorking ? "自动填充功能正常" : "自动填充功能异常");

        } catch (Exception e) {
            log.error("测试插入自动填充失败", e);
            result.put("status", "error");
            result.put("message", "测试失败: " + e.getMessage());
        }

        return result;
    }
}
