package com.siact.energy.cal.common.datasource.debug;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.util.HashMap;
import java.util.Map;

/**
 * 自动填充调试控制器
 * 用于调试和诊断MyBatis Plus自动填充功能在南大通用数据库中的问题
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/debug/autofill")
@Api(tags = "自动填充调试接口")
public class AutoFillDebugController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @GetMapping("/info")
    @ApiOperation("获取自动填充配置信息")
    public Map<String, Object> getAutoFillInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 数据库信息
            Map<String, Object> dbInfo = new HashMap<>();
            dbInfo.put("databaseType", databaseCompatibilityUtil.detectDatabaseType().getName());
            dbInfo.put("productName", databaseCompatibilityUtil.getDatabaseProductName());
            dbInfo.put("productVersion", databaseCompatibilityUtil.getDatabaseProductVersion());
            dbInfo.put("driverName", databaseCompatibilityUtil.getDriverName());
            dbInfo.put("driverVersion", databaseCompatibilityUtil.getDriverVersion());
            dbInfo.put("isGBase", databaseCompatibilityUtil.isGBase());
            dbInfo.put("isPostgreSQL", databaseCompatibilityUtil.isPostgreSQL());
            dbInfo.put("isMysql", databaseCompatibilityUtil.isMysql());
            result.put("database", dbInfo);

            // MetaObjectHandler信息
            Map<String, Object> handlerInfo = new HashMap<>();
            if (metaObjectHandler != null) {
                handlerInfo.put("exists", true);
                handlerInfo.put("className", metaObjectHandler.getClass().getName());
                handlerInfo.put("packageName", metaObjectHandler.getClass().getPackage().getName());
            } else {
                handlerInfo.put("exists", false);
                handlerInfo.put("error", "MetaObjectHandler未注入");
            }
            result.put("metaObjectHandler", handlerInfo);

            // 数据库连接详细信息
            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                Map<String, Object> connectionInfo = new HashMap<>();
                connectionInfo.put("url", metaData.getURL());
                connectionInfo.put("userName", metaData.getUserName());
                connectionInfo.put("supportsBatchUpdates", metaData.supportsBatchUpdates());
                connectionInfo.put("supportsTransactions", metaData.supportsTransactions());
                connectionInfo.put("jdbcMajorVersion", metaData.getJDBCMajorVersion());
                connectionInfo.put("jdbcMinorVersion", metaData.getJDBCMinorVersion());
                result.put("connection", connectionInfo);
            }

            result.put("status", "success");
            result.put("message", "自动填充配置信息获取成功");

        } catch (Exception e) {
            log.error("获取自动填充配置信息失败", e);
            result.put("status", "error");
            result.put("message", "获取配置信息失败: " + e.getMessage());
        }

        return result;
    }

    @GetMapping("/test-compatibility")
    @ApiOperation("测试南大通用数据库兼容性")
    public Map<String, Object> testGBaseCompatibility() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为南大通用数据库
            boolean isGBase = databaseCompatibilityUtil.isGBase();
            result.put("isGBase", isGBase);
            
            if (isGBase) {
                result.put("message", "检测到南大通用数据库");
                
                // 详细的兼容性测试
                Map<String, Object> compatibilityTests = new HashMap<>();
                
                try (Connection connection = dataSource.getConnection()) {
                    DatabaseMetaData metaData = connection.getMetaData();
                    
                    // 基本信息
                    compatibilityTests.put("productName", metaData.getDatabaseProductName());
                    compatibilityTests.put("productVersion", metaData.getDatabaseProductVersion());
                    compatibilityTests.put("driverName", metaData.getDriverName());
                    compatibilityTests.put("url", metaData.getURL());
                    
                    // 功能支持测试
                    compatibilityTests.put("supportsBatchUpdates", metaData.supportsBatchUpdates());
                    compatibilityTests.put("supportsTransactions", metaData.supportsTransactions());
                    compatibilityTests.put("supportsStoredProcedures", metaData.supportsStoredProcedures());
                    
                    // SQL语法支持
                    compatibilityTests.put("supportsOnConflict", databaseCompatibilityUtil.supportsOnConflict());
                    compatibilityTests.put("supportsOnDuplicateKeyUpdate", databaseCompatibilityUtil.supportsOnDuplicateKeyUpdate());
                    
                    result.put("compatibilityTests", compatibilityTests);
                }
                
            } else {
                result.put("message", "未检测到南大通用数据库");
            }
            
            result.put("status", "success");
            
        } catch (Exception e) {
            log.error("南大通用数据库兼容性测试失败", e);
            result.put("status", "error");
            result.put("message", "兼容性测试失败: " + e.getMessage());
        }
        
        return result;
    }

    @GetMapping("/diagnose")
    @ApiOperation("诊断自动填充问题")
    public Map<String, Object> diagnoseAutoFillIssues() {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> issues = new HashMap<>();
        Map<String, String> suggestions = new HashMap<>();
        
        try {
            // 1. 检查MetaObjectHandler
            if (metaObjectHandler == null) {
                issues.put("metaObjectHandler", "MetaObjectHandler未正确注入");
                suggestions.put("metaObjectHandler", "检查@Component注解和包扫描配置");
            } else {
                result.put("metaObjectHandlerClass", metaObjectHandler.getClass().getName());
            }
            
            // 2. 检查数据库类型
            DatabaseCompatibilityUtil.DatabaseType dbType = databaseCompatibilityUtil.detectDatabaseType();
            result.put("detectedDatabaseType", dbType.getName());
            
            if (dbType == DatabaseCompatibilityUtil.DatabaseType.UNKNOWN) {
                issues.put("databaseType", "数据库类型未正确识别");
                suggestions.put("databaseType", "检查数据库驱动和连接配置");
            }
            
            // 3. 检查南大通用数据库特殊情况
            if (databaseCompatibilityUtil.isGBase()) {
                result.put("isGBaseDatabase", true);
                
                // 检查是否正确使用PostgreSQL兼容模式
                if (!databaseCompatibilityUtil.isPostgreSQL()) {
                    issues.put("gbaseCompatibility", "南大通用数据库未正确识别为PostgreSQL兼容");
                    suggestions.put("gbaseCompatibility", "检查数据库类型检测逻辑");
                }
            }
            
            // 4. 检查数据库连接
            try (Connection connection = dataSource.getConnection()) {
                result.put("databaseConnectionOk", true);
                
                if (!connection.getMetaData().supportsBatchUpdates()) {
                    issues.put("batchUpdates", "数据库不支持批量更新");
                    suggestions.put("batchUpdates", "可能影响MyBatis Plus的批量操作");
                }
            } catch (Exception e) {
                issues.put("databaseConnection", "数据库连接失败: " + e.getMessage());
                suggestions.put("databaseConnection", "检查数据源配置和网络连接");
            }
            
            result.put("issues", issues);
            result.put("suggestions", suggestions);
            result.put("status", issues.isEmpty() ? "healthy" : "issues_found");
            result.put("issueCount", issues.size());
            
        } catch (Exception e) {
            log.error("自动填充问题诊断失败", e);
            result.put("status", "error");
            result.put("message", "诊断失败: " + e.getMessage());
        }
        
        return result;
    }
}
