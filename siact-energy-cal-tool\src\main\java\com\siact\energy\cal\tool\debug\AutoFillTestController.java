package com.siact.energy.cal.tool.debug;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import com.siact.energy.cal.tool.entity.dataSource.DataSource;
import com.siact.energy.cal.tool.service.dataSource.DataSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动填充测试控制器
 * 专门用于测试南大通用数据库的自动填充问题
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/debug/autofill-test")
@Api(tags = "自动填充测试接口")
public class AutoFillTestController {

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Autowired(required = false)
    private DataSourceService dataSourceService;

    @Autowired(required = false)
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    @GetMapping("/simple-test")
    @ApiOperation("简单测试自动填充")
    public Map<String, Object> simpleTest() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 开始简单自动填充测试 ===");
            
            // 1. 检查组件状态
            result.put("metaObjectHandlerExists", metaObjectHandler != null);
            result.put("dataSourceServiceExists", dataSourceService != null);
            result.put("databaseUtilExists", databaseCompatibilityUtil != null);
            
            if (metaObjectHandler != null) {
                result.put("metaObjectHandlerClass", metaObjectHandler.getClass().getName());
                log.info("MetaObjectHandler: {}", metaObjectHandler.getClass().getName());
            }
            
            // 2. 检查数据库信息
            if (databaseCompatibilityUtil != null) {
                String dbProduct = databaseCompatibilityUtil.getDatabaseProductName();
                String dbType = databaseCompatibilityUtil.detectDatabaseType().getName();
                boolean isGBase = databaseCompatibilityUtil.isGBase();
                
                result.put("databaseProduct", dbProduct);
                result.put("databaseType", dbType);
                result.put("isGBase", isGBase);
                
                log.info("数据库产品: {}", dbProduct);
                log.info("数据库类型: {}", dbType);
                log.info("是否南大通用: {}", isGBase);
            }
            
            // 3. 执行保存测试
            if (dataSourceService != null) {
                log.info("🚀 开始执行自动填充测试...");

                DataSource testEntity = new DataSource();
                testEntity.setDatabaseName("自动填充测试_" + System.currentTimeMillis());
                testEntity.setDbType(1);
                testEntity.setDatabaseIp("127.0.0.1");
                testEntity.setDatabasePort("3306");
                testEntity.setDb("test");
                testEntity.setUserName("test");
                testEntity.setPassword("test");
                testEntity.setJdbcUrl("********************************");
                testEntity.setDescription("自动填充功能测试");

                log.info("📋 保存前字段状态:");
                log.info("   - createTime: {}", testEntity.getCreateTime());
                log.info("   - updateTime: {}", testEntity.getUpdateTime());
                log.info("   - deleted: {}", testEntity.getDeleted());
                log.info("   - creator: {}", testEntity.getCreator());
                log.info("   - updater: {}", testEntity.getUpdater());

                log.info("💾 执行保存操作...");
                boolean saveResult = dataSourceService.save(testEntity);

                log.info("📊 保存结果: {}", saveResult);
                log.info("📋 保存后字段状态:");
                log.info("   - ID: {}", testEntity.getId());
                log.info("   - createTime: {}", testEntity.getCreateTime());
                log.info("   - updateTime: {}", testEntity.getUpdateTime());
                log.info("   - deleted: {}", testEntity.getDeleted());
                log.info("   - creator: {}", testEntity.getCreator());
                log.info("   - updater: {}", testEntity.getUpdater());

                result.put("saveResult", saveResult);
                result.put("entityId", testEntity.getId());
                result.put("createTimeAfterSave", testEntity.getCreateTime());
                result.put("updateTimeAfterSave", testEntity.getUpdateTime());
                result.put("deletedAfterSave", testEntity.getDeleted());
                result.put("creatorAfterSave", testEntity.getCreator());
                result.put("updaterAfterSave", testEntity.getUpdater());

                // 判断自动填充是否成功
                boolean timeFieldsOk = testEntity.getCreateTime() != null && testEntity.getUpdateTime() != null;
                boolean deletedFieldOk = testEntity.getDeleted() != null;
                boolean autoFillSuccess = timeFieldsOk && deletedFieldOk;

                result.put("timeFieldsOk", timeFieldsOk);
                result.put("deletedFieldOk", deletedFieldOk);
                result.put("autoFillSuccess", autoFillSuccess);

                if (autoFillSuccess) {
                    result.put("status", "success");
                    result.put("message", "🎉 自动填充测试成功！时间和deleted字段已正确填充");
                    log.info("🎉 自动填充测试成功！");
                } else {
                    result.put("status", "failed");
                    result.put("message", "❌ 自动填充失败 - 请检查MetaObjectHandler日志");
                    log.error("❌ 自动填充失败！");
                }
            } else {
                result.put("status", "error");
                result.put("message", "DataSourceService未注入，无法执行测试");
                log.error("❌ DataSourceService未注入！");
            }
            
            log.info("=== 简单自动填充测试完成 ===");
            
        } catch (Exception e) {
            log.error("简单测试失败", e);
            result.put("status", "error");
            result.put("message", "测试异常: " + e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }
        
        return result;
    }

    @GetMapping("/check-handler")
    @ApiOperation("检查MetaObjectHandler是否被调用")
    public Map<String, Object> checkHandler() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("=== 检查MetaObjectHandler调用状态 ===");
            
            if (metaObjectHandler == null) {
                result.put("status", "error");
                result.put("message", "MetaObjectHandler未注入");
                return result;
            }
            
            log.info("MetaObjectHandler类型: {}", metaObjectHandler.getClass().getName());
            log.info("MetaObjectHandler实例: {}", metaObjectHandler);
            
            // 检查是否有自定义的strictFillStrategy方法
            try {
                java.lang.reflect.Method method = metaObjectHandler.getClass().getMethod("strictFillStrategy", 
                    org.apache.ibatis.reflection.MetaObject.class, String.class, java.util.function.Supplier.class);
                result.put("hasStrictFillStrategy", method != null);
                log.info("存在strictFillStrategy方法: {}", method != null);
            } catch (NoSuchMethodException e) {
                result.put("hasStrictFillStrategy", false);
                log.info("不存在strictFillStrategy方法");
            }
            
            result.put("status", "success");
            result.put("message", "MetaObjectHandler检查完成");
            result.put("handlerClass", metaObjectHandler.getClass().getName());
            
        } catch (Exception e) {
            log.error("检查MetaObjectHandler失败", e);
            result.put("status", "error");
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }
}
