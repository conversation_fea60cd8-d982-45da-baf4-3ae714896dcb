package com.siact.energy.cal.tool.debug;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import com.siact.energy.cal.tool.entity.dataSource.DataSource;
import com.siact.energy.cal.tool.service.BaseService;
import com.siact.energy.cal.tool.service.dataSource.DataSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Common模块BaseEntity自动填充调试控制器
 * 专门用于测试common模块BaseEntity在南大通用数据库中的自动填充功能
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/debug/common-autofill")
@Api(tags = "Common BaseEntity自动填充调试接口")
public class CommonBaseEntityDebugController {

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Autowired(required = false)
    private DataSourceService dataSourceService;

    @Autowired(required = false)
    private BaseService baseService;

    @Autowired(required = false)
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    @GetMapping("/info")
    @ApiOperation("获取自动填充配置信息")
    public Map<String, Object> getAutoFillInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 数据库信息
            Map<String, Object> dbInfo = new HashMap<>();
            if (databaseCompatibilityUtil != null) {
                dbInfo.put("databaseType", databaseCompatibilityUtil.detectDatabaseType().getName());
                dbInfo.put("productName", databaseCompatibilityUtil.getDatabaseProductName());
                dbInfo.put("productVersion", databaseCompatibilityUtil.getDatabaseProductVersion());
                dbInfo.put("driverName", databaseCompatibilityUtil.getDriverName());
                dbInfo.put("isGBase", databaseCompatibilityUtil.isGBase());
                dbInfo.put("isPostgreSQL", databaseCompatibilityUtil.isPostgreSQL());
            }
            result.put("database", dbInfo);

            // MetaObjectHandler信息
            Map<String, Object> handlerInfo = new HashMap<>();
            if (metaObjectHandler != null) {
                handlerInfo.put("exists", true);
                handlerInfo.put("className", metaObjectHandler.getClass().getName());
            } else {
                handlerInfo.put("exists", false);
                handlerInfo.put("error", "MetaObjectHandler未注入");
            }
            result.put("metaObjectHandler", handlerInfo);

            // 用户信息
            Map<String, Object> userInfo = new HashMap<>();
            if (baseService != null) {
                userInfo.put("authEnabled", baseService.isEnabled());
                try {
                    Long userId = baseService.getLoginUserId();
                    userInfo.put("currentUserId", userId);
                    userInfo.put("userIdAvailable", userId != null);
                } catch (Exception e) {
                    userInfo.put("userIdError", e.getMessage());
                }
            }
            result.put("user", userInfo);

            result.put("status", "success");

        } catch (Exception e) {
            log.error("获取自动填充配置信息失败", e);
            result.put("status", "error");
            result.put("message", "获取配置信息失败: " + e.getMessage());
        }

        return result;
    }

    @GetMapping("/test-insert")
    @ApiOperation("测试Common BaseEntity插入自动填充")
    public Map<String, Object> testInsertAutoFill() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (dataSourceService == null) {
                result.put("status", "error");
                result.put("message", "DataSourceService未注入，无法测试");
                return result;
            }

            // 创建测试实体（使用common模块BaseEntity）
            DataSource testEntity = new DataSource();
            testEntity.setDatabaseName("自动填充测试_" + System.currentTimeMillis());
            testEntity.setDbType(1); // MySQL
            testEntity.setDatabaseIp("127.0.0.1");
            testEntity.setDatabasePort("3306");
            testEntity.setDb("test");
            testEntity.setUserName("test");
            testEntity.setPassword("test");
            testEntity.setJdbcUrl("********************************");
            testEntity.setDescription("测试自动填充功能");
            
            log.info("=== 插入前的实体状态 ===");
            log.info("creator: {}", testEntity.getCreator());
            log.info("createTime: {}", testEntity.getCreateTime());
            log.info("updater: {}", testEntity.getUpdater());
            log.info("updateTime: {}", testEntity.getUpdateTime());
            log.info("deleted: {}", testEntity.getDeleted());

            // 执行插入
            log.info("=== 开始执行保存操作 ===");
            boolean success = dataSourceService.save(testEntity);
            log.info("保存操作结果: {}", success);

            log.info("=== 插入后的实体状态 ===");
            log.info("ID: {}", testEntity.getId());
            log.info("creator: {}", testEntity.getCreator());
            log.info("createTime: {}", testEntity.getCreateTime());
            log.info("updater: {}", testEntity.getUpdater());
            log.info("updateTime: {}", testEntity.getUpdateTime());
            log.info("deleted: {}", testEntity.getDeleted());

            Map<String, Object> testResult = new HashMap<>();
            testResult.put("insertSuccess", success);
            testResult.put("entityId", testEntity.getId());
            
            Map<String, Object> autoFillResult = new HashMap<>();
            autoFillResult.put("creator", testEntity.getCreator());
            autoFillResult.put("createTime", testEntity.getCreateTime());
            autoFillResult.put("updater", testEntity.getUpdater());
            autoFillResult.put("updateTime", testEntity.getUpdateTime());
            autoFillResult.put("deleted", testEntity.getDeleted());
            
            testResult.put("autoFillFields", autoFillResult);
            
            // 判断自动填充是否成功
            boolean timeFieldsOk = testEntity.getCreateTime() != null && testEntity.getUpdateTime() != null;
            boolean deletedFieldOk = testEntity.getDeleted() != null;
            boolean autoFillWorking = timeFieldsOk && deletedFieldOk;
            
            testResult.put("timeFieldsOk", timeFieldsOk);
            testResult.put("deletedFieldOk", deletedFieldOk);
            testResult.put("autoFillWorking", autoFillWorking);
            
            // 检查creator和updater是否由业务代码设置
            testResult.put("creatorSetByBusiness", testEntity.getCreator() != null);
            testResult.put("updaterSetByBusiness", testEntity.getUpdater() != null);
            
            result.put("testResult", testResult);
            result.put("status", "success");
            
            String message;
            if (autoFillWorking) {
                if (testEntity.getCreator() != null && testEntity.getUpdater() != null) {
                    message = "自动填充功能完全正常，业务代码正确设置了creator和updater";
                } else {
                    message = "时间和deleted字段自动填充正常，但creator/updater未被业务代码设置";
                }
            } else {
                message = "自动填充功能异常，请检查MetaObjectHandler配置";
            }
            result.put("message", message);

        } catch (Exception e) {
            log.error("测试插入自动填充失败", e);
            result.put("status", "error");
            result.put("message", "测试失败: " + e.getMessage());
        }

        return result;
    }

    @GetMapping("/diagnose")
    @ApiOperation("诊断南大通用数据库自动填充问题")
    public Map<String, Object> diagnoseGBaseAutoFill() {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> issues = new HashMap<>();
        Map<String, String> suggestions = new HashMap<>();
        
        try {
            // 1. 检查是否为南大通用数据库
            if (databaseCompatibilityUtil != null) {
                boolean isGBase = databaseCompatibilityUtil.isGBase();
                result.put("isGBaseDatabase", isGBase);
                
                if (isGBase) {
                    result.put("databaseProduct", databaseCompatibilityUtil.getDatabaseProductName());
                    result.put("databaseVersion", databaseCompatibilityUtil.getDatabaseProductVersion());
                    result.put("driverName", databaseCompatibilityUtil.getDriverName());
                    
                    // 检查PostgreSQL兼容性
                    if (!databaseCompatibilityUtil.isPostgreSQL()) {
                        issues.put("gbaseCompatibility", "南大通用数据库未正确识别为PostgreSQL兼容");
                        suggestions.put("gbaseCompatibility", "检查DatabaseCompatibilityUtil.detectDatabaseType()方法");
                    }
                } else {
                    result.put("message", "当前不是南大通用数据库环境");
                }
            }
            
            // 2. 检查MetaObjectHandler
            if (metaObjectHandler == null) {
                issues.put("metaObjectHandler", "MetaObjectHandler未正确注入");
                suggestions.put("metaObjectHandler", "检查@Component注解和包扫描配置");
            }
            
            // 3. 检查用户服务
            if (baseService != null && baseService.isEnabled()) {
                try {
                    Long userId = baseService.getLoginUserId();
                    if (userId == null) {
                        issues.put("userService", "无法获取当前登录用户ID");
                        suggestions.put("userService", "检查用户认证状态和BaseService实现");
                    }
                } catch (Exception e) {
                    issues.put("userService", "获取用户信息异常: " + e.getMessage());
                    suggestions.put("userService", "检查认证服务配置");
                }
            }
            
            result.put("issues", issues);
            result.put("suggestions", suggestions);
            result.put("status", issues.isEmpty() ? "healthy" : "issues_found");
            result.put("issueCount", issues.size());
            
        } catch (Exception e) {
            log.error("诊断失败", e);
            result.put("status", "error");
            result.put("message", "诊断失败: " + e.getMessage());
        }
        
        return result;
    }

    @GetMapping("/test-metaobject")
    @ApiOperation("直接测试MetaObjectHandler")
    public Map<String, Object> testMetaObjectHandler() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (metaObjectHandler == null) {
                result.put("status", "error");
                result.put("message", "MetaObjectHandler未注入");
                return result;
            }

            log.info("=== 直接测试MetaObjectHandler ===");
            log.info("MetaObjectHandler类型: {}", metaObjectHandler.getClass().getName());

            // 创建测试实体
            DataSource testEntity = new DataSource();
            testEntity.setDatabaseName("MetaObjectHandler测试_" + System.currentTimeMillis());
            testEntity.setDbType(1);
            testEntity.setDatabaseIp("127.0.0.1");
            testEntity.setDatabasePort("3306");
            testEntity.setDb("test");
            testEntity.setUserName("test");
            testEntity.setPassword("test");
            testEntity.setJdbcUrl("********************************");

            log.info("测试实体创建完成，准备测试自动填充");

            result.put("status", "success");
            result.put("message", "MetaObjectHandler测试准备完成，请查看日志");
            result.put("metaObjectHandlerClass", metaObjectHandler.getClass().getName());

        } catch (Exception e) {
            log.error("MetaObjectHandler测试失败", e);
            result.put("status", "error");
            result.put("message", "测试失败: " + e.getMessage());
        }

        return result;
    }
}
