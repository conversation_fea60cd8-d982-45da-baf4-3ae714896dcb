package com.siact.energy.cal.common.datasource.component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 自动填充配置检查器
 * 在应用启动时检查MyBatis Plus自动填充相关配置是否正确
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Component
@Order(3) // 在其他组件之后执行
public class AutoFillConfigChecker implements CommandLineRunner {

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Autowired(required = false)
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(String... args) throws Exception {
        checkAutoFillConfiguration();
    }

    private void checkAutoFillConfiguration() {
        log.info("=== MyBatis Plus 自动填充配置检查 ===");
        
        boolean configOk = true;
        
        // 1. 检查MetaObjectHandler
        if (metaObjectHandler != null) {
            log.info("✅ MetaObjectHandler已注入: {}", metaObjectHandler.getClass().getName());
        } else {
            log.error("❌ MetaObjectHandler未注入！自动填充功能将不工作");
            configOk = false;
        }
        
        // 2. 检查SqlSessionFactory
        if (sqlSessionFactory != null) {
            log.info("✅ SqlSessionFactory已注入: {}", sqlSessionFactory.getClass().getName());
            
            // 检查Configuration类型
            try {
                org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();

                if (configuration != null) {
                    log.info("✅ Configuration已配置: {}", configuration.getClass().getSimpleName());

                    // 检查是否为MyBatis Plus的Configuration
                    if (configuration instanceof com.baomidou.mybatisplus.core.MybatisConfiguration) {
                        log.info("✅ 使用MyBatis Plus Configuration");

                        // 简化检查，主要确认MetaObjectHandler是否注入
                        if (metaObjectHandler != null) {
                            log.info("✅ MetaObjectHandler已注入到Spring容器");
                            log.info("   类型: {}", metaObjectHandler.getClass().getName());
                            log.info("   MyBatis Plus应该能够自动发现并使用此Handler");
                        } else {
                            log.error("❌ MetaObjectHandler未注入到Spring容器！");
                            configOk = false;
                        }
                    } else {
                        log.warn("⚠️ 使用的是标准MyBatis Configuration，不是MyBatis Plus Configuration");
                        log.warn("   Configuration类型: {}", configuration.getClass().getName());
                        log.warn("   这可能导致自动填充功能不工作");
                        configOk = false;
                    }
                } else {
                    log.error("❌ Configuration未配置！");
                    configOk = false;
                }
            } catch (Exception e) {
                log.error("❌ 检查SqlSessionFactory配置时出错: {}", e.getMessage());
                configOk = false;
            }
        } else {
            log.error("❌ SqlSessionFactory未注入！");
            configOk = false;
        }
        
        // 3. 总结
        if (configOk) {
            log.info("🎉 MyBatis Plus 自动填充配置检查通过！");
            log.info("💡 如果自动填充仍然不工作，请检查:");
            log.info("   1. 实体类是否正确继承了BaseEntity");
            log.info("   2. 字段是否有@TableField(fill = FieldFill.INSERT)注解");
            log.info("   3. 是否使用了正确的save()方法");
            log.info("   4. 查看MetaObjectHandler的日志输出");
        } else {
            log.error("💥 MyBatis Plus 自动填充配置有问题！自动填充功能可能不工作");
        }
        
        log.info("=== 自动填充配置检查完成 ===");
    }
}
