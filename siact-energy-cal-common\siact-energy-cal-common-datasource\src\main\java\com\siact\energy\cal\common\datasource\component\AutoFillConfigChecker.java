package com.siact.energy.cal.common.datasource.component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 自动填充配置检查器
 * 在应用启动时检查MyBatis Plus自动填充相关配置是否正确
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Component
@Order(3) // 在其他组件之后执行
public class AutoFillConfigChecker implements CommandLineRunner {

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Autowired(required = false)
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(String... args) throws Exception {
        checkAutoFillConfiguration();
    }

    private void checkAutoFillConfiguration() {
        log.info("=== MyBatis Plus 自动填充配置检查 ===");
        
        boolean configOk = true;
        
        // 1. 检查MetaObjectHandler
        if (metaObjectHandler != null) {
            log.info("✅ MetaObjectHandler已注入: {}", metaObjectHandler.getClass().getName());
        } else {
            log.error("❌ MetaObjectHandler未注入！自动填充功能将不工作");
            configOk = false;
        }
        
        // 2. 检查SqlSessionFactory
        if (sqlSessionFactory != null) {
            log.info("✅ SqlSessionFactory已注入: {}", sqlSessionFactory.getClass().getName());
            
            // 检查GlobalConfig
            try {
                com.baomidou.mybatisplus.core.MybatisConfiguration configuration = 
                    (com.baomidou.mybatisplus.core.MybatisConfiguration) sqlSessionFactory.getConfiguration();
                
                if (configuration != null) {
                    log.info("✅ MybatisConfiguration已配置");
                    
                    // 检查GlobalConfig中的MetaObjectHandler
                    com.baomidou.mybatisplus.core.config.GlobalConfig globalConfig = 
                        configuration.getGlobalConfig();
                    
                    if (globalConfig != null) {
                        log.info("✅ GlobalConfig已配置");
                        
                        MetaObjectHandler configuredHandler = globalConfig.getMetaObjectHandler();
                        if (configuredHandler != null) {
                            log.info("✅ GlobalConfig中的MetaObjectHandler已配置: {}", 
                                configuredHandler.getClass().getName());
                            
                            if (configuredHandler == metaObjectHandler) {
                                log.info("✅ MetaObjectHandler实例匹配");
                            } else {
                                log.warn("⚠️ MetaObjectHandler实例不匹配");
                            }
                        } else {
                            log.error("❌ GlobalConfig中的MetaObjectHandler未配置！");
                            configOk = false;
                        }
                    } else {
                        log.error("❌ GlobalConfig未配置！");
                        configOk = false;
                    }
                } else {
                    log.error("❌ MybatisConfiguration未配置！");
                    configOk = false;
                }
            } catch (Exception e) {
                log.error("❌ 检查SqlSessionFactory配置时出错: {}", e.getMessage());
                configOk = false;
            }
        } else {
            log.error("❌ SqlSessionFactory未注入！");
            configOk = false;
        }
        
        // 3. 总结
        if (configOk) {
            log.info("🎉 MyBatis Plus 自动填充配置检查通过！");
            log.info("💡 如果自动填充仍然不工作，请检查:");
            log.info("   1. 实体类是否正确继承了BaseEntity");
            log.info("   2. 字段是否有@TableField(fill = FieldFill.INSERT)注解");
            log.info("   3. 是否使用了正确的save()方法");
            log.info("   4. 查看MetaObjectHandler的日志输出");
        } else {
            log.error("💥 MyBatis Plus 自动填充配置有问题！自动填充功能可能不工作");
        }
        
        log.info("=== 自动填充配置检查完成 ===");
    }
}
