package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * MybatisPlusMetaObjectHandler
 * 支持两种BaseEntity：
 * 1. common模块BaseEntity: createTime, updateTime, deleted, creator, updater
 * 2. tool模块BaseEntity: createTime, updateTime, createId, createName
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();

        // 通用字段：createTime, updateTime（两种BaseEntity都有）
        if (metaObject.hasSetter("createTime") && getFieldValByName("createTime", metaObject) == null) {
            strictInsertFill(metaObject, "createTime", Date.class, date);
            log.debug("自动填充 createTime: {}", date);
        }

        if (metaObject.hasSetter("updateTime") && getFieldValByName("updateTime", metaObject) == null) {
            strictInsertFill(metaObject, "updateTime", Date.class, date);
            log.debug("自动填充 updateTime: {}", date);
        }

        // common模块BaseEntity字段：deleted
        if (metaObject.hasSetter("deleted") && getFieldValByName("deleted", metaObject) == null) {
            strictInsertFill(metaObject, "deleted", Integer.class, 0);
            log.debug("自动填充 deleted: 0");
        }

        // tool模块BaseEntity字段：createId, createName（只在值为null时才填充默认值）
        // 这些字段通常由业务代码手动设置，只有在业务代码未设置时才进行默认填充

        // 填充创建者ID（tool模块BaseEntity字段）
        if (metaObject.hasSetter("createId") && getFieldValByName("createId", metaObject) == null) {
            // 设置默认值，业务代码可以覆盖
            strictInsertFill(metaObject, "createId", Integer.class, 0);
            log.debug("自动填充 createId: 0 (默认值)");
        }

        // 填充创建者姓名（tool模块BaseEntity字段）
        if (metaObject.hasSetter("createName") && getFieldValByName("createName", metaObject) == null) {
            // 设置默认值，业务代码可以覆盖
            strictInsertFill(metaObject, "createName", String.class, "system");
            log.debug("自动填充 createName: system (默认值)");
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Date date = new Date();

        // 更新时间字段（两种BaseEntity都有）
        if (metaObject.hasSetter("updateTime")) {
            strictUpdateFill(metaObject, "updateTime", Date.class, date);
            log.debug("自动填充 updateTime: {}", date);
        }

        // 注意：updater字段由业务代码手动设置，不在此处自动填充
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }
}
