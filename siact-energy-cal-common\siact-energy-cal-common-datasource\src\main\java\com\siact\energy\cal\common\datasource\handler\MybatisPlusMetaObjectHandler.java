package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * MybatisPlusMetaObjectHandler
 * 主要处理common模块BaseEntity的自动填充：creator, createTime, updater, updateTime, deleted
 * creator和updater字段只在值为null时才自动填充，业务代码设置的值优先
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            Date date = new Date();
            String entityClass = metaObject.getOriginalObject().getClass().getSimpleName();

            log.info("=== MyBatis Plus 插入自动填充开始 ===");
            log.info("实体类: {}", entityClass);

            // 检查所有字段的存在性
            boolean hasCreateTime = metaObject.hasSetter("createTime");
            boolean hasUpdateTime = metaObject.hasSetter("updateTime");
            boolean hasDeleted = metaObject.hasSetter("deleted");
            boolean hasCreator = metaObject.hasSetter("creator");
            boolean hasUpdater = metaObject.hasSetter("updater");

            log.info("字段检查 - createTime: {}, updateTime: {}, deleted: {}, creator: {}, updater: {}",
                hasCreateTime, hasUpdateTime, hasDeleted, hasCreator, hasUpdater);

            // common模块BaseEntity字段：createTime
            if (hasCreateTime) {
                Object currentValue = getFieldValByName("createTime", metaObject);
                log.info("createTime 当前值: {}", currentValue);
                if (currentValue == null) {
                    strictInsertFill(metaObject, "createTime", Date.class, date);
                    log.info("✅ 自动填充 createTime: {}", date);
                } else {
                    log.info("⚠️ createTime 已有值，跳过自动填充");
                }
            }

            // common模块BaseEntity字段：updateTime
            if (hasUpdateTime) {
                Object currentValue = getFieldValByName("updateTime", metaObject);
                log.info("updateTime 当前值: {}", currentValue);
                if (currentValue == null) {
                    strictInsertFill(metaObject, "updateTime", Date.class, date);
                    log.info("✅ 自动填充 updateTime: {}", date);
                } else {
                    log.info("⚠️ updateTime 已有值，跳过自动填充");
                }
            }

            // common模块BaseEntity字段：deleted
            if (hasDeleted) {
                Object currentValue = getFieldValByName("deleted", metaObject);
                log.info("deleted 当前值: {}", currentValue);
                if (currentValue == null) {
                    strictInsertFill(metaObject, "deleted", Integer.class, 0);
                    log.info("✅ 自动填充 deleted: 0");
                } else {
                    log.info("⚠️ deleted 已有值，跳过自动填充");
                }
            }

            // common模块BaseEntity字段：creator（记录状态，不自动填充）
            if (hasCreator) {
                Object currentValue = getFieldValByName("creator", metaObject);
                log.info("creator 当前值: {} (由业务代码设置)", currentValue);
            }

            // common模块BaseEntity字段：updater（记录状态，不自动填充）
            if (hasUpdater) {
                Object currentValue = getFieldValByName("updater", metaObject);
                log.info("updater 当前值: {} (由业务代码设置)", currentValue);
            }

            log.info("=== MyBatis Plus 插入自动填充完成 ===");

        } catch (Exception e) {
            log.error("插入自动填充失败", e);
            throw e;
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            Date date = new Date();
            String entityClass = metaObject.getOriginalObject().getClass().getSimpleName();

            log.info("=== MyBatis Plus 更新自动填充开始 ===");
            log.info("实体类: {}", entityClass);

            // common模块BaseEntity字段：updateTime
            if (metaObject.hasSetter("updateTime")) {
                strictUpdateFill(metaObject, "updateTime", Date.class, date);
                log.info("✅ 自动填充 updateTime: {}", date);
            } else {
                log.warn("⚠️ 实体类 {} 没有 updateTime 字段", entityClass);
            }

            // common模块BaseEntity字段：updater（记录状态，不自动填充）
            if (metaObject.hasSetter("updater")) {
                Object currentValue = getFieldValByName("updater", metaObject);
                log.info("updater 当前值: {} (由业务代码设置)", currentValue);
            }

            log.info("=== MyBatis Plus 更新自动填充完成 ===");

        } catch (Exception e) {
            log.error("更新自动填充失败", e);
            throw e;
        }
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }
}
