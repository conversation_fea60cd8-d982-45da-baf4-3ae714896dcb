package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * MybatisPlusMetaObjectHandler
 * 主要处理common模块BaseEntity的自动填充：creator, createTime, updater, updateTime, deleted
 * creator和updater字段只在值为null时才自动填充，业务代码设置的值优先
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        // 关键调试日志：确认方法是否被调用
        log.info("🔥🔥🔥 MetaObjectHandler.insertFill() 被调用！实体类: {}",
            metaObject.getOriginalObject().getClass().getSimpleName());

        try {
            Date date = new Date();
            log.info("准备填充字段，当前时间: {}", date);

            // 检查字段是否存在
            log.info("检查字段存在性:");
            log.info("- createTime字段存在: {}", metaObject.hasSetter("createTime"));
            log.info("- updateTime字段存在: {}", metaObject.hasSetter("updateTime"));
            log.info("- deleted字段存在: {}", metaObject.hasSetter("deleted"));

            // 检查字段当前值
            log.info("检查字段当前值:");
            log.info("- createTime当前值: {}", getFieldValByName("createTime", metaObject));
            log.info("- updateTime当前值: {}", getFieldValByName("updateTime", metaObject));
            log.info("- deleted当前值: {}", getFieldValByName("deleted", metaObject));

            // 执行填充
            log.info("开始执行字段填充...");
            strictInsertFill(metaObject, "createTime", Date.class, date);
            log.info("✅ createTime 填充完成");

            strictInsertFill(metaObject, "updateTime", Date.class, date);
            log.info("✅ updateTime 填充完成");

            strictInsertFill(metaObject, "deleted", Integer.class, 0);
            log.info("✅ deleted 填充完成");

            // 验证填充结果
            log.info("验证填充结果:");
            log.info("- createTime填充后值: {}", getFieldValByName("createTime", metaObject));
            log.info("- updateTime填充后值: {}", getFieldValByName("updateTime", metaObject));
            log.info("- deleted填充后值: {}", getFieldValByName("deleted", metaObject));

            log.info("🎉 MetaObjectHandler.insertFill() 执行完成！");

        } catch (Exception e) {
            log.error("❌ MetaObjectHandler.insertFill() 执行失败", e);
            throw e; // 重新抛出异常，便于调试
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("🔥🔥🔥 MetaObjectHandler.updateFill() 被调用！实体类: {}",
            metaObject.getOriginalObject().getClass().getSimpleName());

        try {
            Date date = new Date();
            log.info("准备填充updateTime字段，当前时间: {}", date);

            strictUpdateFill(metaObject, "updateTime", Date.class, date);
            log.info("✅ updateTime 更新填充完成");

        } catch (Exception e) {
            log.error("❌ MetaObjectHandler.updateFill() 执行失败", e);
            throw e;
        }
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }

}
