package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * MybatisPlusMetaObjectHandler
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            log.debug("开始执行插入自动填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

            Date date = new Date();

            // 填充创建时间
            if (hasField(metaObject, "createTime")) {
                strictInsertFill(metaObject, "createTime", Date.class, date);
                log.debug("自动填充 createTime: {}", date);
            }

            // 填充更新时间
            if (hasField(metaObject, "updateTime")) {
                strictInsertFill(metaObject, "updateTime", Date.class, date);
                log.debug("自动填充 updateTime: {}", date);
            }

            // 填充删除标志
            if (hasField(metaObject, "deleted")) {
                strictInsertFill(metaObject, "deleted", Integer.class, 0);
                log.debug("自动填充 deleted: 0");
            }

            log.debug("插入自动填充完成");
            // 注意：creator、updater、status字段由业务代码手动设置，不在此处自动填充
        } catch (Exception e) {
            log.error("插入自动填充失败，实体类: {}, 错误信息: {}",
                metaObject.getOriginalObject().getClass().getSimpleName(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            log.debug("开始执行更新自动填充，实体类: {}", metaObject.getOriginalObject().getClass().getSimpleName());

            Date date = new Date();

            // 填充更新时间
            if (hasField(metaObject, "updateTime")) {
                strictUpdateFill(metaObject, "updateTime", Date.class, date);
                log.debug("自动填充 updateTime: {}", date);
            }

            log.debug("更新自动填充完成");
            // 注意：updater字段由业务代码手动设置，不在此处自动填充
        } catch (Exception e) {
            log.error("更新自动填充失败，实体类: {}, 错误信息: {}",
                metaObject.getOriginalObject().getClass().getSimpleName(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查实体是否包含指定字段
     */
    private boolean hasField(MetaObject metaObject, String fieldName) {
        return metaObject.hasSetter(fieldName);
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }
}
