package com.siact.energy.cal.common.datasource.component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.util.Date;

/**
 * 自动填充测试组件
 * 用于测试和验证MyBatis Plus自动填充功能在南大通用数据库中的工作状态
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Component
@Order(2) // 在DatabaseInfoPrinter之后执行
public class AutoFillTestComponent implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    @Override
    public void run(String... args) throws Exception {
        try {
            testAutoFillConfiguration();
        } catch (Exception e) {
            log.warn("自动填充测试失败: {}", e.getMessage());
        }
    }

    private void testAutoFillConfiguration() {
        log.info("=== MyBatis Plus 自动填充功能测试 ===");
        
        // 1. 检查MetaObjectHandler是否正确注入
        if (metaObjectHandler == null) {
            log.error("❌ MetaObjectHandler 未正确注入！");
            return;
        } else {
            log.info("✅ MetaObjectHandler 已正确注入: {}", metaObjectHandler.getClass().getSimpleName());
        }

        // 2. 测试数据库连接
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String productName = metaData.getDatabaseProductName();
            String driverName = metaData.getDriverName();
            
            log.info("数据库产品: {}", productName);
            log.info("JDBC驱动: {}", driverName);
            
            // 特别检查南大通用数据库
            if (productName != null && (productName.toLowerCase().contains("gbase") || productName.contains("南大通用"))) {
                log.info("🔍 检测到南大通用数据库，进行特殊兼容性检查...");
                testGBaseCompatibility(connection);
            }
            
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage());
        }

        // 3. 创建模拟MetaObject进行测试
        testMetaObjectHandlerFunctionality();
        
        log.info("=== 自动填充功能测试完成 ===");
    }

    private void testGBaseCompatibility(Connection connection) {
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            
            log.info("南大通用数据库详细信息:");
            log.info("  - 产品名称: {}", metaData.getDatabaseProductName());
            log.info("  - 产品版本: {}", metaData.getDatabaseProductVersion());
            log.info("  - 驱动名称: {}", metaData.getDriverName());
            log.info("  - 驱动版本: {}", metaData.getDriverVersion());
            log.info("  - JDBC主版本: {}", metaData.getJDBCMajorVersion());
            log.info("  - JDBC次版本: {}", metaData.getJDBCMinorVersion());
            
            // 检查是否支持批量操作
            log.info("  - 支持批量更新: {}", metaData.supportsBatchUpdates());
            log.info("  - 支持事务: {}", metaData.supportsTransactions());
            
        } catch (Exception e) {
            log.warn("南大通用数据库兼容性检查失败: {}", e.getMessage());
        }
    }

    private void testMetaObjectHandlerFunctionality() {
        log.info("测试 MetaObjectHandler 功能:");
        
        try {
            // 创建一个测试实体
            TestEntity testEntity = new TestEntity();
            
            // 这里我们无法直接创建MetaObject，但可以记录相关信息
            log.info("  - MetaObjectHandler 类型: {}", metaObjectHandler.getClass().getName());
            log.info("  - 测试实体创建成功: {}", testEntity.getClass().getSimpleName());
            log.info("  - 当前时间: {}", new Date());
            
            // 检查MetaObjectHandler的方法
            log.info("  - insertFill 方法存在: {}", hasMethod(metaObjectHandler.getClass(), "insertFill"));
            log.info("  - updateFill 方法存在: {}", hasMethod(metaObjectHandler.getClass(), "updateFill"));
            
        } catch (Exception e) {
            log.error("MetaObjectHandler 功能测试失败: {}", e.getMessage());
        }
    }

    private boolean hasMethod(Class<?> clazz, String methodName) {
        try {
            clazz.getMethod(methodName, MetaObject.class);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * 测试实体类
     */
    public static class TestEntity {
        private Long id;
        private Date createTime;
        private Date updateTime;
        private Long creator;
        private Long updater;
        private Integer deleted;

        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }
        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
        public Long getCreator() { return creator; }
        public void setCreator(Long creator) { this.creator = creator; }
        public Long getUpdater() { return updater; }
        public void setUpdater(Long updater) { this.updater = updater; }
        public Integer getDeleted() { return deleted; }
        public void setDeleted(Integer deleted) { this.deleted = deleted; }
    }
}
