package com.siact.energy.cal.common.datasource.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * SqlSessionFactory配置类
 * 专门处理数据库ID提供者的配置
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Configuration
public class SqlSessionFactoryConfiguration {

    @Autowired
    private DatabaseIdProvider databaseIdProvider;

    /**
     * 自定义SqlSessionFactory，支持数据库ID提供者
     */
    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        
        // 设置数据库ID提供者 - 这是关键配置
        factory.setDatabaseIdProvider(databaseIdProvider);
        
        // 设置Mapper文件位置
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
        
        // 设置类型别名包
        factory.setTypeAliasesPackage("com.siact.energy.cal.*.entity");
        
        return factory.getObject();
    }
}
