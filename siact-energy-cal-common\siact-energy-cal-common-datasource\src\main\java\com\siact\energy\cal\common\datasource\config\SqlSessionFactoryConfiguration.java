package com.siact.energy.cal.common.datasource.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * SqlSessionFactory配置类
 * 专门处理数据库ID提供者的配置
 * 重要：必须正确配置GlobalConfig以支持自动填充功能
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
@Configuration
public class SqlSessionFactoryConfiguration {

    @Autowired
    private DatabaseIdProvider databaseIdProvider;

    @Autowired(required = false)
    private MetaObjectHandler metaObjectHandler;

    /**
     * 自定义SqlSessionFactory，支持数据库ID提供者
     * 重要：必须正确配置GlobalConfig以支持自动填充功能
     */
    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        log.info("🔧 开始配置自定义SqlSessionFactory");

        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);

        // 设置数据库ID提供者 - 这是关键配置
        factory.setDatabaseIdProvider(databaseIdProvider);
        log.info("✅ 已设置DatabaseIdProvider");

        // 🔥 关键：配置GlobalConfig以支持自动填充
        GlobalConfig globalConfig = new GlobalConfig();
        if (metaObjectHandler != null) {
            globalConfig.setMetaObjectHandler(metaObjectHandler);
            log.info("✅ 已设置MetaObjectHandler: {}", metaObjectHandler.getClass().getSimpleName());
        } else {
            log.warn("⚠️ MetaObjectHandler未找到，自动填充功能可能不工作");
        }
        factory.setGlobalConfig(globalConfig);

        // 设置Mapper文件位置
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
        log.info("✅ 已设置Mapper文件位置");

        // 设置类型别名包
        factory.setTypeAliasesPackage("com.siact.energy.cal.*.entity");
        log.info("✅ 已设置类型别名包");

        log.info("🎉 SqlSessionFactory配置完成");
        return factory.getObject();
    }
}
